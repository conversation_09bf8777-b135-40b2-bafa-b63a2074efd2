{"go.buildOnSave": "workspace", "go.lintOnSave": "workspace", "go.vetOnSave": "workspace", "go.buildTags": "", "go.buildFlags": [], "go.lintFlags": [], "go.vetFlags": [], "go.coverOnSave": false, "go.useCodeSnippetsOnFunctionSuggest": true, "go.formatTool": "goimports", "go.formatFlags": [], "go.inferGopath": false, "go.testOnSave": false, "go.testFlags": ["-v"], "go.testTimeout": "30s", "go.coverageDecorator": {"type": "highlight", "coveredHighlightColor": "rgba(64,128,128,0.5)", "uncoveredHighlightColor": "rgba(128,64,64,0.25)", "coveredGutterStyle": "blockblue", "uncoveredGutterStyle": "blockred"}, "go.coverageOptions": "showBothCoveredAndUncoveredCode", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "files.eol": "\n"}